import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Eye, BadgeEuro } from "lucide-react";
import { motion } from "framer-motion";

const items = [
  { icon: ShieldChe<PERSON>, title: "GDPR conforme",
    desc: "I tuoi dati restano in UE, trattati secondo art. 5 GDPR." },
  { icon: Lock, title: "Crittografia & PCI-DSS",
    desc: "Pagamenti protetti dallo standard PCI-DSS." },
  { icon: Eye, title: "Controlli privacy",
    desc: "Decidi tu quali dati condividere, in ogni momento." },
  { icon: BadgeEuro, title: "Zero costi nascosti",
    desc: "Mostriamo sempre il prezzo finale: niente sorprese." },
];

export default function SecuritySection() {
  const cardVariant = {
    hidden:   { opacity: 0, y: 40, scale: .95 },
    visible:  (i:number)=>({
      opacity: 1, y: 0, scale: 1,
      transition:{ duration:.6, delay:i*0.15 }
    })
  };
  return (
    <section className="bg-white py-16" id="security">
      <div className="section-container">
        <h2 className="section-title text-center">Sicurezza e Trasparenza</h2>
        <p className="section-subtitle text-center mx-auto">
          La tua privacy è la nostra priorità: dati protetti e pagamenti sicuri
        </p>

        <div className="mt-12 grid grid-cols-1 md:grid-cols-4 gap-8">
          {items.map(({icon:Icon, title, desc}, i) => (
            <motion.div
              key={title}
              custom={i}
              variants={cardVariant}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, amount: 0.25 }}
              className="card p-6 rounded-xl border border-border bg-white
                         shadow-sm text-center transition-transform duration-300"
            >
              <div className="w-12 h-12 rounded-full bg-accent/10
                              flex items-center justify-center mx-auto mb-4">
                <Icon className="w-6 h-6 text-accent" />
              </div>
              <h3 className="text-lg font-semibold mb-2 text-charcoal">
                {title}
              </h3>
              <p className="text-sm text-muted-foreground">{desc}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
