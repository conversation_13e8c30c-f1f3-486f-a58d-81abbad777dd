// FlowSection.tsx
import {
  MessageCircle,
  DollarSign,
  Clock
} from "lucide-react";
import { motion } from "framer-motion";
import AnimatedBackground from "./AnimatedBackground";

const steps = [
  {
    icon: <MessageCircle className="w-6 h-6 text-white" />,
    title: "Parla o scrivi",
    description:
      "Puoi interagire con il tuo assistente in 25 lingue"
  },
  {
    icon: <DollarSign className="w-6 h-6 text-white" />,
    title: "L’IA lavora per te h24",
    description:
      "Trova automaticamente le migliori opzioni e tratta il prezzo ideale."
  },
  {
    icon: <Clock className="w-6 h-6 text-white" />,
    title: "Confermi & prenoti",
    description:
      "Un tap per approvare, prenotazione istantanea."
  }
];

export const FlowSection = () => (

        <section className="relative bg-gradient-to-b from-white to-muted/30 overflow-hidden" id="how-it-works">
      {/* Animated background elements */}
  <AnimatedBackground/>
    <div className="section-container">
      <h3 className="section-title text-center">Come funziona CatchUp</h3>
      <p className="section-subtitle text-center mx-auto">
        Tre passi e hai già prenotato.
      </p>

      {/* --- TIMELINE DESKTOP --- */}
      <div className="relative max-w-4xl mx-auto mt-16">
        {/* linea grigia di base */}
        <div className="hidden md:block absolute top-10 left-0 right-0 h-1 bg-gray-200" />

        {/* linea arancio animata con Framer */}
        <motion.div
          className="hidden md:block absolute top-10 left-0 right-0 h-1 bg-gradient-to-r from-accent via-accent to-accent origin-left opacity-60"
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          transition={{ duration: 1.4, ease: "easeOut" }}
          viewport={{ once: true, amount: 0.35 }}
        />

        {/* puntini decorativi */}
        <div className="hidden md:flex absolute top-10 left-0 right-0 h-1 items-center justify-between px-0">
          {steps.map((_, i) => (
            <span
              key={i}
              className={`w-4 h-4 rounded-full shadow-lg border-2 border-white ${
                i === 0
                  ? "bg-accent"
                  : i === 1
                  ? "bg-accent/80"
                  : "bg-accent/60"
              }`}
            />
          ))}
        </div>

        {/* --- CARD DEI PASSI --- */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
          {steps.map((step, i) => (
            <motion.div
              key={i}
              className="relative flex flex-col items-center text-center"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{
                duration: 1.2,        // ← da 0.6 s → 1.2 s (singola card)
                delay: i * 0.3        // ← spacing fra card più lento (prima 0.15)
              }}
              viewport={{ once: true, amount: 0.25 }}
            >
              {/* cerchio icona */}
              <div className="w-16 h-16 rounded-full bg-accent flex items-center justify-center mb-6 shadow-lg text-white">
                {step.icon}
                <span className="absolute -top-3 -right-3 w-8 h-8 rounded-full bg-white border-2 border-accent flex items-center justify-center text-sm font-bold text-accent">
                  {i + 1}
                </span>
              </div>

              <h3 className="text-xl font-semibold mb-3 text-charcoal">
                {step.title}
              </h3>
              <p className="text-muted-foreground">{step.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  </section>
);
