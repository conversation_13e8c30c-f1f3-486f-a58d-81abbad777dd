
import React from 'react';
import { Globe, Clock, DollarSign } from 'lucide-react';

const FeatureCard: React.FC<{ 
  icon: React.ReactNode, 
  title: string, 
  description: string,
  badge?: string 
}> = ({ 
  icon, 
  title, 
  description,
  badge 
}) => {
  return (
    <div className="feature-card animate-fade-in cursor-pointer transform transition-all duration-200 hover:scale-[1.03] hover:shadow-lg relative">
      {badge && (
        <div className="absolute -top-2 -right-2 bg-gradient-to-r from-accent to-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-md animate-pulse">
          {badge}
        </div>
      )}
      <div className="feature-icon animated-icon">
        {icon}
      </div>
      <h3 className="text-xl font-semibold mb-2 text-charcoal">{title}</h3>
      <p className="text-muted-foreground">{description}</p>
    </div>
  );
};
// TODO on mobile, create a carousel of features
const FeaturesSection: React.FC = () => {



  return (
    <section id="features" className="bg-muted/10 py-16">
      <div className="section-container">
        <h2 className="section-title text-center">Cosa fa il tuo assistente personale IA</h2>
        <p className="section-subtitle text-center mx-auto">
          
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
          <FeatureCard 
            icon={<Globe className="w-6 h-6 text-accent" />} 
            title="Più lo usi, più ti conosce." 
            description="Ti propone ciò che ti piace e di cui hai bisongo. "
          />
          
          <FeatureCard 
            icon={<DollarSign className="w-6 h-6 text-accent" />} 
            title="Solo piacere, niente stress" 
            description="Scegli, confermi e paghi in un tap. Zero chiamate, zero attese, zero stress"
          />
          
          <FeatureCard 
            icon={<Clock className="w-6 h-6 text-accent" />} 
            title="C’è sempre se hai bisogno di lui" 
            description="Il tuo assistente personale IA, non dorme e non va in vacanza, c’è sempre, 24 h 7/7, e parla e capisce 25 lingue"
            badge="Voice β"
          />
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
