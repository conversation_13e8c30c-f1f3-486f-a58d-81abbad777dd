import React, { useState } from "react";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

const AnnouncementBanner: React.FC = () => {
  const [isVisible, setIsVisible] = useState(true);
  const navigate = useNavigate();
  if (!isVisible) return null;

  return (
    <div className="relative bg-gradient-to-r from-accent via-orange-500 to-pink-500 text-white py-3 px-4 text-center animate-pulse">
      <div className="absolute inset-0 bg-gradient-to-r from-accent/90 via-orange-500/90 to-pink-500/90 animate-float"></div>
      <div className="relative z-10 flex items-center justify-center gap-4">
        <p className="text-sm md:text-base font-medium">
          🚀 <strong>Iscriviti alla waiting list per accedere al progetto di lancio</strong>
        </p>
        <Button
          variant="secondary"
          size="sm"
          className="bg-white text-gray-900 hover:bg-gray-100 font-semibold px-4 py-2 rounded-md transition-colors"
          onClick={() => {
            // TODO: Add waiting list signup logic
            console.log("Joining waiting list...");
            navigate("/waitlist");
          }}
        >
          Iscriviti Ora
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsVisible(false)}
          className="absolute right-2 text-white hover:bg-white/20 h-8 w-8 p-0"
          aria-label="Chiudi banner"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export default AnnouncementBanner;