-- Create campaigns table
CREATE TABLE public.campaigns (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  email_subject TEXT,
  email_content TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create user_campaigns table to track send status
CREATE TABLE public.user_campaigns (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL,
  campaign_id UUID NOT NULL REFERENCES public.campaigns(id) ON DELETE CASCADE,
  status TEXT NOT NULL DEFAULT 'ToSend',
  sent_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(user_id, campaign_id)
);

-- Enable RLS
ALTER TABLE public.campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_campaigns ENABLE ROW LEVEL SECURITY;

-- RLS policies for campaigns
CREATE POLICY "Admins can manage campaigns" ON public.campaigns
FOR ALL USING (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Everyone can view campaigns" ON public.campaigns
FOR SELECT USING (true);

-- RLS policies for user_campaigns
CREATE POLICY "Admins can manage user campaigns" ON public.user_campaigns
FOR ALL USING (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Users can view their own campaigns" ON public.user_campaigns
FOR SELECT USING (user_id = auth.uid());

-- Create update triggers
CREATE TRIGGER update_campaigns_updated_at
BEFORE UPDATE ON public.campaigns
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_user_campaigns_updated_at
BEFORE UPDATE ON public.user_campaigns
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Insert default "Joined Waiting List" campaign
INSERT INTO public.campaigns (name, description, email_subject, email_content) 
VALUES (
  'Joined Waiting List',
  'Welcome email sent when users join the waiting list',
  'Benvenuto in CatchUp! Sei il #{position} nella lista d''attesa',
  'Grazie per esserti iscritto alla lista d''attesa di CatchUp!'
);

-- Remove the old trigger
DROP TRIGGER IF EXISTS on_waitlist_signup ON public.waitlist;