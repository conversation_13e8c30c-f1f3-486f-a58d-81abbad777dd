import { Button } from "./ui/button";

interface ButtonJoinWaitingListProps {
  analyticsLabel: string;
}

const ButtonJoinWaitingList = ( { analyticsLabel }: ButtonJoinWaitingListProps ) => {
 
  return (
    <Button
      size="lg"
      className="bg-accent hover:bg-accent/90 text-white text-lg py-6 px-8 relative overflow-hidden group transform hover:scale-105 transition-all duration-200"
      onClick={() => {
        window.location.href = "/waitlist";
      }}
      // onClick={handleJoinWaitlist}
      data-analytics={analyticsLabel}
    >
      <span className="absolute inset-0 w-full h-full bg-white/20 transform -translate-x-full group-hover:translate-x-0 transition-transform duration-300"></span>
      <span className="relative">Entra nella community!</span>
    </Button>
  );
};

export default ButtonJoinWaitingList;
