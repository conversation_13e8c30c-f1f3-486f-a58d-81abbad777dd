import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.50.3';
import { Resend } from "npm:resend@2.0.0";
import React from 'npm:react@18.3.1';
import { renderAsync } from 'npm:@react-email/components@0.0.22';
import { BroadcastEmail } from './_templates/broadcast-email.tsx';

const resend = new Resend(Deno.env.get("RESEND_API_KEY"));
const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
);

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

interface BroadcastEmailRequest {
  subject: string;
  content: string;
  filters?: {
    marketing_consent?: boolean;
    company?: string;
    role?: string;
  };
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { subject, content, filters = {} }: BroadcastEmailRequest = await req.json();

    console.log(`Starting broadcast email: "${subject}"`);

    // Build query with filters
    let query = supabase.from('waitlist').select('email, first_name, last_name, company');
    
    if (filters.marketing_consent !== undefined) {
      query = query.eq('marketing_consent', filters.marketing_consent);
    }
    if (filters.company) {
      query = query.ilike('company', `%${filters.company}%`);
    }
    if (filters.role) {
      query = query.ilike('role', `%${filters.role}%`);
    }

    const { data: subscribers, error } = await query;

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    if (!subscribers || subscribers.length === 0) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: "No subscribers found matching the criteria" 
      }), {
        status: 400,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      });
    }

    console.log(`Found ${subscribers.length} subscribers to email`);

    // Send emails in batches to avoid rate limiting
    const batchSize = 50;
    const results = [];
    
    for (let i = 0; i < subscribers.length; i += batchSize) {
      const batch = subscribers.slice(i, i + batchSize);
      const batchPromises = batch.map(async (subscriber) => {
        try {
          const html = await renderAsync(
            React.createElement(BroadcastEmail, {
              firstName: subscriber.first_name,
              lastName: subscriber.last_name,
              company: subscriber.company,
              content,
            })
          );

          const emailResponse = await resend.emails.send({
            from: "CatchUp <<EMAIL>>",
            to: [subscriber.email],
            subject,
            html,
          });

          return { 
            email: subscriber.email, 
            success: true, 
            messageId: emailResponse.data?.id 
          };
        } catch (error) {
          console.error(`Failed to send to ${subscriber.email}:`, error);
          return { 
            email: subscriber.email, 
            success: false, 
            error: error.message 
          };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Add delay between batches to respect rate limits
      if (i + batchSize < subscribers.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    console.log(`Broadcast complete: ${successCount} sent, ${failureCount} failed`);

    return new Response(JSON.stringify({ 
      success: true,
      totalSubscribers: subscribers.length,
      successCount,
      failureCount,
      results: results
    }), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders,
      },
    });
  } catch (error: any) {
    console.error("Error in send-broadcast-email function:", error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

serve(handler);