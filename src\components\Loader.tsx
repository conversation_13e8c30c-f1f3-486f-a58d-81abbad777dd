import React from "react";

function Loader() {
  return (
    <div className="fixed inset-0 bg-gradient-to-b from-white to-muted/30 flex items-center justify-center z-50">
      {/* Animated background elements matching the site style */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-10 -right-10 w-40 h-40 bg-accent/5 rounded-full animate-float opacity-70"></div>
        <div className="absolute top-40 -left-20 w-60 h-60 bg-accent/10 rounded-full animate-float opacity-50" style={{animationDelay: '1s'}}></div>
        <div className="absolute bottom-20 right-10 w-20 h-20 bg-accent/20 rounded-full animate-float opacity-60" style={{animationDelay: '2s'}}></div>
      </div>

      {/* Main loader content */}
      <div className="relative z-10 flex flex-col items-center space-y-8">
        {/* Logo */}
        <div className="animate-fade-in">
          <img 
            src="/logo_full.png" 
            alt="CatchUp" 
            className="h-12 md:h-16 animate-pulse"
          />
        </div>

        {/* Animated spinner */}
        <div className="relative animate-fade-in delay-200">
          <div className="w-16 h-16 border-4 border-accent/20 border-t-accent rounded-full animate-spin"></div>
          <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-accent/40 rounded-full animate-spin" style={{animationDirection: 'reverse', animationDuration: '1.5s'}}></div>
        </div>

        {/* Loading text */}
        <div className="text-center animate-fade-in delay-300">
          <h2 className="text-xl md:text-2xl font-semibold text-charcoal font-poppins mb-2">
            Stiamo preparando tutto per te
          </h2>
          <p className="text-muted-foreground text-sm md:text-base">
            Il tuo assistente personale si sta caricando...
          </p>
        </div>

        {/* Animated dots */}
        <div className="flex space-x-1 animate-fade-in delay-500">
          <div className="w-2 h-2 bg-accent rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-accent rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
          <div className="w-2 h-2 bg-accent rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
        </div>
      </div>
    </div>
  );
}

export default Loader;