// FAQSection.tsx – React + Tailwind + Framer Motion (accessibile)
import { motion } from "framer-motion";
import { ChevronDown } from "lucide-react";
import { useState } from "react";
import AnimatedBackground from "./AnimatedBackground";

interface FAQ { q: string; a: string }

const faqs: FAQ[] = [
  {
    q: "Quali lingue supporta CatchUp?",
    a: "CatchUp riconosce e parla oltre 25 lingue, tra cui italiano, inglese, francese, tedesco, spagnolo, cinese e giapponese. Puoi chattare o parlare nella tua lingua preferita: l’assistente risponderà con voce naturale."
  },
  {
    q: "Come funziona la negoziazione automatica?",
    a: "L’IA monitora in tempo reale gli slot liberi di saloni estetici, parrucchieri/barbieri, centri wellness, tavoli liberi in ristoranti, posti liberi in teatri e cinema, camere invendute in hotel. Per le fasce orarie meno richieste contratta sconti last-minute con i partner, così ottieni sempre il miglior prezzo disponibile."
  },
  {
    q: "Posso annullare o modificare una prenotazione?",
    a: "Sì. Nell’app trovi il pulsante \"Annulla\" o \"Modifica orario\". Le eventuali penali dipendono dalla policy del partner e vengono mostrate chiaramente prima della conferma."
  },
  {
    q: "Che tipi di servizi posso prenotare?",
    a: "Al momento: trattamenti estetici (viso, mani, epilazione), parrucchiere o barber, massaggi e day-spa. Stiamo integrando ristoranti e attività leisure: arriveranno in una release successiva."
  },
  {
    q: "Ci sono costi nascosti o commissioni?",
    a: "Mai. Il prezzo che vedi è quello finale: includiamo già tasse e eventuali sconti. Nessuna commissione aggiuntiva alla cassa."
  },
  {
    q: "Come evolverà CatchUp in futuro?",
    a: "Da marketplace di prenotazione diventerà un assistente personale: gestirà agenda, reminder, pagamenti ricorrenti e potrà automatizzare micro-task quotidiani, trasformando la tua lista di cose da fare in \"fatto!\"."
  }
];


const FAQItem = ({ q, a, id }: { q: string; a: string; id: string }) => {
  const [open, setOpen] = useState(false);
  return (
    <div className="border-b border-border last:border-0">
      <h3 className="m-0">
        <button
          className="flex w-full items-center justify-between py-4 text-left text-lg font-medium focus:outline-none focus-visible:ring"
          aria-expanded={open}
          aria-controls={id}
          onClick={() => setOpen(!open)}
        >
          {q}
          <ChevronDown
            className={`h-5 w-5 shrink-0 transition-transform ${
              open ? "rotate-180 text-accent" : ""
            }`}
          />
        </button>
      </h3>

      <motion.div
        id={id}
        initial={false}
        animate={open ? "open" : "collapsed"}
        variants={{
          open: { height: "auto", opacity: 1 },
          collapsed: { height: 0, opacity: 0 }
        }}
        transition={{ duration: 0.4, ease: "easeOut" }}
        className="overflow-hidden"
      >
        <p className="pb-4 text-muted-foreground">{a}</p>
      </motion.div>
    </div>
  );
};

export default function FAQSection() {
  return (
    <section className="relative bg-gradient-to-b from-white to-muted/30 overflow-hidden" id="faq">
    {/* Animated background elements */}
<AnimatedBackground/>
      <div className="section-container">
        <h2 className="section-title text-center">Domande frequenti</h2>
        <p className="section-subtitle text-center mx-auto">
          Tutto ciò che devi sapere su CatchUp
        </p>

        <motion.div
          className="max-w-3xl mx-auto mt-12"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={{
            hidden: { opacity: 0, y: 40 },
            visible: { opacity: 1, y: 0, transition: { duration: 0.8 } }
          }}
        >
          {faqs.map((f, i) => (
            <FAQItem key={i} q={f.q} a={f.a} id={`faq-${i}`} />
          ))}
        </motion.div>
      </div>
    </section>
  );
}
