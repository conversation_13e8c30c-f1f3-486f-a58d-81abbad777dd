import React, { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";

// Define message types
type MessageSender = "assistant" | "user";
type MessageStatus = "typing" | "complete";
type MessageType = "message" | "confirmation" | "searching";

interface Message {
  id: number;
  type: MessageType;
  sender: MessageSender;
  content: string;
  status: MessageStatus;
  visible: boolean;
  delay: number;
}

// Message component for better animation control
// AI Message Bubble component
const AIMessageBubble: React.FC<{
  message: Message;
  onComplete?: () => void;
}> = ({ message, onComplete }) => {
  return (
    <div
      className="bg-muted/20 rounded-xl p-4 mb-4 animate-fade-in opacity-0"
      style={{
        animationDelay: "100ms",
        animationDuration: "400ms",
        animationFillMode: "forwards",
      }}
    >
      <div className="flex items-center mb-3">
        <div className="w-8 h-8 rounded-full bg-accent text-white flex items-center justify-center text-sm font-bold">
          CU
        </div>
        <div className="ml-3">
          <p className="text-sm font-medium">CatchUp Assistant</p>
        </div>
      </div>
      <p className="text-sm mb-2">
        {message.status === "typing" ? (
          <TypingText
            text={message.content}
            onComplete={() => onComplete && onComplete()}
          />
        ) : (
          message.content
        )}
      </p>
    </div>
  );
};

// User Message Bubble component
const UserMessageBubble: React.FC<{
  message: Message;
}> = ({ message }) => {
  return (
    <div
      className="flex justify-end mb-4 animate-fade-in opacity-0"
      style={{
        animationDelay: "100ms",
        animationDuration: "400ms",
        animationFillMode: "forwards",
      }}
    >
      <div className="bg-muted/30 rounded-xl p-3 max-w-[85%]">
        <p className="text-sm">{message.content}</p>
      </div>
    </div>
  );
};
const SearchingMessageBubble: React.FC<{
  message: Message;
}> = ({ message }) => {
  return (
    <div
      className="flex justify-start mb-4 animate-fade-in opacity-0"
      style={{
        animationDelay: "100ms",
        animationDuration: "400ms",
        animationFillMode: "forwards",
      }}
    >
      <div className="bg-muted/30 rounded-xl p-3 max-w-[85%]">
        <p className="text-sm">{message.content}</p>
      </div>
    </div>
  );
};
// Improved typing text effect component with more natural typing
const TypingText: React.FC<{ text: string; onComplete: () => void }> = ({
  text,
  onComplete,
}) => {
  const [displayedText, setDisplayedText] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (currentIndex < text.length) {
      // Variable typing speed based on character type for more natural effect
      let delay = 30; // Base delay

      // Longer pauses after punctuation
      if ([".", ",", "!", "?", ":"].includes(text[currentIndex - 1])) {
        delay = 150 + Math.random() * 100;
      }
      // Shorter delays for common characters
      else {
        // Randomize typing speed slightly for realism
        delay = 30 + Math.random() * 50;
      }

      const timeout = setTimeout(() => {
        setDisplayedText((prev) => prev + text[currentIndex]);
        setCurrentIndex((prev) => prev + 1);
      }, delay);

      return () => clearTimeout(timeout);
    } else {
      // Add a small delay before completing to make it feel more natural
      const completeTimeout = setTimeout(() => {
        onComplete();
      }, 300);

      return () => clearTimeout(completeTimeout);
    }
  }, [currentIndex, text, onComplete]);

  return (
    <>
      {displayedText}
      <span className="animate-pulse">|</span>
    </>
  );
};

interface ChatSimulationProps {
  className?: string;
}

const ChatSimulation: React.FC<ChatSimulationProps> = ({ className }) => {
  // Define the chat messages
  const initialMessages2: Message[] = [
    {
      id: 1,
      sender: "assistant",
      type: "message",
      content: "Come posso aiutarti oggi?",
      status: "complete",
      delay: 1200,
      visible: true,
    },
    {
      id: 2,
      sender: "user",
      type: "message",
      content: "Vorrei prenotare un tavolo per 2 persone stasera in centro",
      status: "complete",
      delay: 1200,
      visible: false,
    },
    {
      id: 3,
      sender: "assistant",
      type: "message",
      content: "Bene. Quanto è il importo massimo che puoi spendere?",
      status: "typing",
      delay: 2500,
      visible: false,
    },
    {
      id: 4,
      sender: "user",
      type: "message",
      content: "Massimo 40 euro",
      status: "complete",
      delay: 5000,
      visible: false,
    },
    {
      id: 5,
      sender: "assistant",
      type: "message",
      content: "Sto cercando le migliori opzioni disponibili per stasera...",
      status: "typing",
      delay: 6500,
      visible: false,
    },
    {
      id: 6,
      sender: "assistant",
      type: "searching",
      content: "Controllo Ristorante Osteria degli Amini...",
      status: "complete",
      delay: 6500,
      visible: false,
    },
    {
      id: 7,
      sender: "assistant",
      type: "searching",
      content: "Controllo Ristorante Il Caminetto...",
      status: "complete",
      delay: 1200,
      visible: false,
    },
    {
      id: 8,
      sender: "assistant",
      type: "searching",
      content: "Controllo Ristornate La Pergola...",
      status: "complete",
      delay: 1200,
      visible: false,
    },

    {
      id: 9,
      sender: "assistant",
      type: "message",
      content:
        'Ho trovato un tavolo per 2 al ristorante "La Pergola" alle 20:30. Hanno un\'offerta speciale del -15% sul menu completo, per un totale di 39 euro.',
      status: "typing",
      delay: 9000,
      visible: false,
    },
  ];
// ChatSimulation.tsx – sostituisci solo l’array dei messaggi
const initialMessages1: Message[] = [
  {
    id: 1,
    sender: "assistant",
    type: "message",
    content: "Ciao! Che coccola cerchi oggi?",
    status: "complete",
    delay: 1200,
    visible: true,
  },
  {
    id: 2,
    sender: "user",
    type: "message",
    content: "Vorrei manicure e massaggio schiena per domani pomeriggio",
    status: "complete",
    delay: 1200,
    visible: false,
  },
  {
    id: 3,
    sender: "assistant",
    type: "message",
    content: "Perfetto! Hai un budget indicativo?",
    status: "typing",
    delay: 2400,
    visible: false,
  },
  {
    id: 4,
    sender: "user",
    type: "message",
    content: "Massimo 60 euro in totale",
    status: "complete",
    delay: 2600,
    visible: false,
  },
  {
    id: 5,
    sender: "assistant",
    type: "message",
    content: "Cerco le migliori opzioni disponibili…",
    status: "typing",
    delay: 3600,
    visible: false,
  },
  // searching bubbles…
  {
    id: 9,
    sender: "assistant",
    type: "message",
    content:
      'Ho trovato “Relax Spa” alle 18:30 con pacchetto manicure + massaggio 45\' a 59 € (-18 %).',
    status: "typing",
    delay: 9000,
    visible: false,
  },
];

  const [messages, setMessages] = useState<Message[]>( Math.random() < 0.5 ? initialMessages1 : initialMessages2);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const chatAreaRef = useRef<HTMLDivElement>(null);
  const initialMessages = initialMessages2 || initialMessages1;


  // Set fixed dimensions for the chat container
  useEffect(() => {
    // Create placeholder content to ensure consistent sizing
    const setFixedDimensions = () => {
      if (chatAreaRef.current) {
        // Set fixed height and width to prevent layout shifts
        chatAreaRef.current.style.height = "450px";

        // Ensure the chat container has a fixed height too
        if (chatContainerRef.current) {
          chatContainerRef.current.style.height = "350px";
        }
      }
    };

    setFixedDimensions();

    // Handle window resize
    window.addEventListener("resize", setFixedDimensions);
    return () => window.removeEventListener("resize", setFixedDimensions);
  }, []);

  // Function to update message status
  const completeMessage = (id: number) => {
    setMessages((prev) =>
      prev.map((msg) =>
        msg.id === id ? { ...msg, status: "complete" as MessageStatus } : msg
      )
    );
  };

  // Improved smooth scrolling when new messages appear
  useEffect(() => {
    if (chatContainerRef.current) {
      // Use requestAnimationFrame for smoother scrolling
      requestAnimationFrame(() => {
        chatContainerRef.current?.scrollTo({
          top: chatContainerRef.current.scrollHeight,
          behavior: "smooth",
        });
      });
    }
  }, [messages, showConfirmation]);

  // Sequence the messages to appear one after another with improved timing
  useEffect(() => {
    // Funzione per mostrare un messaggio specifico
    const showMessage = (id: number, delay: number) => {
      return setTimeout(() => {
        setMessages((prev) =>
          prev.map((msg) => (msg.id === id ? { ...msg, visible: true } : msg))
        );
      }, delay);
    };

    // Mostra i messaggi in sequenza
    // Base delay in milliseconds
    const baseDelay = 1200;

    let currentDelay = baseDelay;

    const timers = initialMessages.map((msg, index) => {
      const delay = index === 0 ? currentDelay : (currentDelay += msg.delay);
      return showMessage(msg.id, delay);
    });

    // Mostra la scheda di conferma dopo l'ultimo messaggio
    const confirmationTimer = setTimeout(() => {
      setShowConfirmation(true);
    }, currentDelay + 1000);

    timers.push(confirmationTimer);

    // Pulizia dei timer
    return () => {
      timers.forEach((timer) => clearTimeout(timer));
    };
  }, []);

  // Create placeholder messages for consistent layout
  const placeholderMessages = (
    <>
      <div className="bg-muted/20 rounded-xl p-4 mb-4 opacity-0">
        <div className="flex items-center mb-3">
          <div className="w-8 h-8 rounded-full"></div>
          <div className="ml-3">
            <p className="text-sm font-medium invisible">CatchUp Assistant</p>
          </div>
        </div>
        <p className="text-sm mb-2 invisible">Placeholder text for sizing</p>
      </div>
      <div className="flex justify-end mb-4 opacity-0">
        <div className="bg-muted/30 rounded-xl p-3 max-w-[85%]">
          <p className="text-sm invisible">Placeholder user message</p>
        </div>
      </div>
    </>
  );

  return (
    <div className={`relative w-full max-w-md mx-auto ${className}`}>
      {/* Highlight effect around the chat UI */}
      <div className="absolute -inset-4 bg-gradient-to-r from-accent/20 to-accent/10 rounded-3xl blur-lg animate-pulse opacity-70"></div>

      {/* Chat UI + Confirmation Card */}
      <div
        ref={chatAreaRef}
        className="bg-white rounded-2xl shadow-xl border border-border p-3 relative flex flex-col"
      >
        {/* Chat messages container with fixed height and scrolling */}
        <div
          ref={chatContainerRef}
          className="overflow-y-auto mb-4 scroll-smooth"
        >
          {/* Empty space at the top to allow scrolling to the beginning */}
          <div className="h-4"></div>

          {/* Hidden placeholder messages to maintain layout */}
          <div className="absolute opacity-0 pointer-events-none">
            {placeholderMessages}
          </div>

          {/* Render messages dynamically */}
          {messages.map((message) =>
            message.visible && message.type === "message" ? (
              message.sender === "assistant" ? (
                <AIMessageBubble
                  key={message.id}
                  message={message}
                  onComplete={
                    message.status === "typing"
                      ? () => completeMessage(message.id)
                      : undefined
                  }
                />
              ) : (
                <UserMessageBubble key={message.id} message={message} />
              )
            ) : message.visible && message.type === "searching" ? (
              <SearchingMessageBubble key={message.id} message={message} />
            ) : null
          )}

          {/* Empty space at the bottom to allow scrolling to the end */}
          <div className="h-4"></div>
        </div>

        {/* Confirmation Card */}
        {showConfirmation && (
          <div
            className="bg-white rounded-xl border border-accent/20 p-4 shadow-md animate-fade-in opacity-0"
            style={{
              animationDelay: "200ms",
              animationDuration: "600ms",
              animationFillMode: "forwards",
            }}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold text-charcoal">La Pergola</h3>
              <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                Confermato
              </span>
            </div>

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Data:</span>
                <span className="font-medium">Oggi, 20:30</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Persone:</span>
                <span className="font-medium">2</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Offerta:</span>
                <span className="font-medium text-accent">Sconto 15%</span>
              </div>
            </div>

            <div className="mt-4">
              <Button
                className="w-full bg-accent/10 hover:bg-accent/20 text-accent"
                variant="outline"
              >
                Dettagli prenotazione
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatSimulation;
