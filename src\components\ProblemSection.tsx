
import React from 'react';

const ProblemSection: React.FC = () => {
  return (
    <section className="bg-white py-16">
      <div className="section-container">
        <h2 className="section-title text-center">Da ore di ricerca a un semplice tap</h2>
        <p className="section-subtitle text-center mx-auto">
          Perché perdere tempo quando CatchUp può fare tutto al posto tuo?
        </p>
        
        <div className="max-w-3xl mx-auto mt-12 rounded-xl overflow-hidden shadow-lg border border-border">
          <div className="grid grid-cols-1 md:grid-cols-2">
            <div className="bg-muted/20 p-6 md:p-8">
              <h3 className="text-xl font-semibold mb-6 text-charcoal">Ricerca manuale</h3>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <span className="text-red-500 mr-2">✖</span>
                  <span>Fino a 30 min spesi per cercare e prenotare cio' di cui hai bisogno</span>
                </li>
                <li className="flex items-start">
                  <span className="text-red-500 mr-2">✖</span>
                  <span>Confronti manuali tra vari siti</span>
                </li>
                <li className="flex items-start">
                  <span className="text-red-500 mr-2">✖</span>
                  <span>Barriere linguistiche mentre sei in viaggio</span>
                </li>
                <li className="flex items-start">
                  <span className="text-red-500 mr-2">✖</span>
                  <span>Prezzi fissi senza possibilità di negoziazione</span>
                </li>
                <li className="flex items-start">
                  <span className="text-red-500 mr-2">✖</span>
                  <span>Limitato agli orari di apertura delle attività</span>
                </li>
              </ul>
            </div>
            
            <div className="bg-white p-6 md:p-8 border-t md:border-t-0 md:border-l border-border">
              <h3 className="text-xl font-semibold mb-6 text-accent">Con CatchUp</h3>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>Basta 1 tap: tempo risparmiato per ciò che ami</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>L'AI cerca e confronta automaticamente le migliori opzioni</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>Comunicazione vocale naturale in 25+ lingue</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>Negoziazione automatica per gli slot non prenotati</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>Disponibile 24/7, capisce i tuoi bisogni e li anticipa prendendosi cura di te</span>
                </li>
              </ul>
            </div>
          </div>
          
          <div className="bg-accent/5 p-4 text-center font-medium text-lg border-t border-border">
            <span className="text-charcoal">Tempo medio risparmiato: </span>
            <span className="text-accent font-bold">23 minuti</span>
            <span className="text-charcoal"> per ogni prenotazione</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProblemSection;
