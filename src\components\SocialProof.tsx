
import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, Star } from 'lucide-react';

interface Testimonial {
  name: string;
  role: string;
  stars: number;
  quote: string;
  avatar: string;
}

const testimonials: Testimonial[] = [
  {
    name: "<PERSON>",
    role: "Manager, <PERSON>",
    stars: 5,
    quote: " mi ha trovato un tavolo in un ristorante sold-out in 30 secondi. Mi ha fatto risparmiare tempo e ho ricevuto pure uno sconto!",
    avatar: "https://randomuser.me/api/portraits/men/32.jpg"
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    role: "Designer, Roma",
    stars: 5,
    quote: "Ero a Parigi e non parlo francese. CatchUp ha gestito tutto in italiano per me, prenotando un tour privato con uno sconto del 20%!",
    avatar: "https://randomuser.me/api/portraits/women/44.jpg"
  },
  {
    name: "<PERSON>",
    role: "<PERSON><PERSON><PERSON><PERSON><PERSON>, Torino",
    stars: 4,
    quote: "L'app più utile che abbia mai usato. A mezzanotte ho chiesto un massaggio per il giorno dopo ed in 2 secondi ho ricevuto la conferma.",
    avatar: "https://randomuser.me/api/portraits/men/85.jpg"
  }
];

const SocialProof: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  
  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
  };
  
  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };
  
  return (
    <section id="testimonials" className="bg-white py-16">
      <div className="section-container">
        <h2 className="section-title text-center">Cosa dicono i nostri clienti</h2>
        <p className="section-subtitle text-center mx-auto">
          Migliaia di persone usano già CatchUp per le loro prenotazioni
        </p>
        
        {/* Testimonial Carousel */}
        <div className="max-w-4xl mx-auto mt-12 relative">
          <div className="bg-white rounded-2xl shadow-lg border border-border p-6 md:p-8">
            <div className="flex flex-col md:flex-row md:items-center gap-6">
              <div className="flex-shrink-0 mx-auto md:mx-0">
                <img 
                  src={testimonials[currentIndex].avatar} 
                  alt={testimonials[currentIndex].name} 
                  className="w-24 h-24 rounded-full border-2 border-accent"
                />
              </div>
              
              <div>
                <div className="flex mb-2 justify-center md:justify-start">
                  {Array(testimonials[currentIndex].stars).fill(0).map((_, i) => (
                    <Star key={i} className="w-5 h-5 fill-accent text-accent" />
                  ))}
                  {Array(5 - testimonials[currentIndex].stars).fill(0).map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-muted" />
                  ))}
                </div>
                
                <blockquote className="text-lg md:text-xl italic mb-4 text-center md:text-left">
                  "{testimonials[currentIndex].quote}"
                </blockquote>
                
                <div className="text-center md:text-left">
                  <div className="font-semibold text-charcoal">{testimonials[currentIndex].name}</div>
                  <div className="text-sm text-muted-foreground">{testimonials[currentIndex].role}</div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex justify-center mt-6 space-x-2">
            <Button 
              variant="outline" 
              size="icon" 
              onClick={prevTestimonial}
              className="rounded-full border-accent/20 hover:bg-accent/5 hover:border-accent"
            >
              <ChevronLeft className="w-5 h-5 text-accent" />
            </Button>
            
            {testimonials.map((_, i) => (
              <Button 
                key={i}
                variant="outline" 
                size="icon" 
                onClick={() => setCurrentIndex(i)}
                className={`rounded-full w-3 h-3 p-0 ${i === currentIndex ? 'bg-accent border-accent' : 'border-accent/20 hover:border-accent'}`}
              />
            ))}
            
            <Button 
              variant="outline" 
              size="icon" 
              onClick={nextTestimonial}
              className="rounded-full border-accent/20 hover:bg-accent/5 hover:border-accent"
            >
              <ChevronRight className="w-5 h-5 text-accent" />
            </Button>
          </div>
        </div>
        
        {/* Stats */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="bg-muted/10 rounded-xl p-6 text-center">
            <div className="text-3xl md:text-4xl font-bold text-accent mb-2">+15%</div>
            <p className="text-muted-foreground">revenue ai nostri partner</p>
          </div>
          
          <div className="bg-muted/10 rounded-xl p-6 text-center">
            <div className="text-3xl md:text-4xl font-bold text-accent mb-2">40k</div>
            <p className="text-muted-foreground">richieste vocali processate al giorno</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SocialProof;
