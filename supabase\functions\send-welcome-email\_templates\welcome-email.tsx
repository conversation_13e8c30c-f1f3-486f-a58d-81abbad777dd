import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Text,
  Hr,
  Section,
} from 'npm:@react-email/components@0.0.22';
import * as React from 'npm:react@18.3.1';

interface WelcomeEmailProps {
  firstName: string;
  lastName: string;
  company?: string;
  waitlistPosition: number;
}

export const WelcomeEmail = ({
  firstName,
  lastName,
  company,
  waitlistPosition,
}: WelcomeEmailProps) => (
  <Html>
    <Head />
    <Preview>Benvenuto in CatchUp! La rivoluzione degli appuntamenti inizia qui.</Preview>
    <Body style={main}>
      <Container style={container}>
        <Section style={logoContainer}>
          <Img
            src="https://ec2792b2-a97d-4eeb-aa29-cbf1c9b954a6.lovableproject.com/logo_full.png"
            width="200"
            height="auto"
            alt="CatchUp Logo"
            style={logo}
          />
        </Section>
        
        <Heading style={h1}>Ciao {firstName}! 👋</Heading>
        
        <Text style={text}>
          Grazie per esserti iscritto alla lista d'attesa di <strong>CatchUp</strong>! 
          Sei ufficialmente il numero <strong>#{waitlistPosition}</strong> nella nostra lista esclusiva.
        </Text>

        {company && (
          <Text style={text}>
            È fantastico sapere che rappresenti <strong>{company}</strong>. 
            Siamo entusiasti di vedere come CatchUp potrà trasformare il modo in cui gestite gli appuntamenti!
          </Text>
        )}

        <Text style={text}>
          <strong>Cosa succede ora?</strong>
        </Text>

        <Text style={text}>
          📅 <strong>Accesso anticipato:</strong> Riceverai un invito esclusivo prima del lancio pubblico<br/>
          🚀 <strong>Aggiornamenti di sviluppo:</strong> Ti terremo aggiornato sui nostri progressi<br/>
          💡 <strong>Feedback prioritario:</strong> La tua opinione plasmerà il futuro di CatchUp<br/>
          🎁 <strong>Offerte esclusive:</strong> Sconti e benefit riservati solo ai primi utenti
        </Text>

        <Hr style={hr} />

        <Text style={text}>
          <strong>Perché CatchUp sarà rivoluzionario?</strong>
        </Text>

        <Text style={text}>
          🤖 <strong>AI intelligente</strong> per ottimizzare automaticamente i tuoi appuntamenti<br/>
          📱 <strong>Interfaccia intuitiva</strong> che rende la prenotazione un piacere<br/>
          ⚡ <strong>Sincronizzazione istantanea</strong> con tutti i tuoi strumenti preferiti<br/>
          📊 <strong>Analytics avanzate</strong> per massimizzare la produttività
        </Text>

        <Hr style={hr} />

        <Text style={text}>
          Vuoi essere ancora più coinvolto? Seguici sui social per sneak peek esclusivi:
        </Text>

        <Section style={socialContainer}>
          <Link href="https://linkedin.com/company/catchup" style={socialLink}>
            LinkedIn
          </Link>
          <Link href="https://twitter.com/catchup" style={socialLink}>
            Twitter
          </Link>
          <Link href="https://instagram.com/catchup" style={socialLink}>
            Instagram
          </Link>
        </Section>

        <Text style={footerText}>
          Hai domande? Rispondi direttamente a questa email - leggiamo ogni messaggio!
        </Text>

        <Text style={footerText}>
          A presto,<br/>
          <strong>Il Team CatchUp</strong>
        </Text>

        <Hr style={hr} />

        <Text style={disclaimer}>
          Hai ricevuto questa email perché ti sei iscritto alla lista d'attesa di CatchUp. 
          Se non dovessi più ricevere aggiornamenti, puoi disiscriverti in qualsiasi momento.
        </Text>
      </Container>
    </Body>
  </Html>
);

export default WelcomeEmail;

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Oxygen", Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  borderRadius: '8px',
  margin: '40px auto',
  padding: '40px',
  maxWidth: '600px',
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
};

const logoContainer = {
  textAlign: 'center' as const,
  marginBottom: '32px',
};

const logo = {
  margin: '0 auto',
};

const h1 = {
  color: '#1a1a1a',
  fontSize: '28px',
  fontWeight: '700',
  lineHeight: '1.3',
  margin: '0 0 24px',
};

const text = {
  color: '#525252',
  fontSize: '16px',
  lineHeight: '1.6',
  margin: '0 0 16px',
};

const hr = {
  borderColor: '#e6e6e6',
  margin: '24px 0',
};

const socialContainer = {
  textAlign: 'center' as const,
  margin: '24px 0',
};

const socialLink = {
  color: '#6366f1',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  margin: '0 16px',
};

const footerText = {
  color: '#525252',
  fontSize: '16px',
  lineHeight: '1.6',
  margin: '24px 0 8px',
};

const disclaimer = {
  color: '#9ca3af',
  fontSize: '12px',
  lineHeight: '1.5',
  margin: '16px 0 0',
  textAlign: 'center' as const,
};