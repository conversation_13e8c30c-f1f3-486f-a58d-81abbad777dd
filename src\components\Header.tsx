import React from "react";
import { Button } from "@/components/ui/button";
const Header: React.FC = () => {
  return (
    <header className="w-full py-4 bg-white/90 backdrop-blur-sm sticky top-0 z-50 border-b border-border">
      <div className="container flex items-center justify-between">
        <div className="flex items-end space-x-2">
          <span className="text-2xl font-semibold text-gray-900">
            <img
              alt="CatchUp Logo"
              className="h-8 md:h-10"
              src="/logo_full.png"
            />
          </span>

          <span className="text-xs text-primary-600 -mb-1">
            Diamo valore al tuo tempo.
          </span>
        </div>

        <nav className="hidden md:flex items-center space-x-8">
          <a
            href="#features"
            className="text-sm font-medium text-muted-foreground hover:text-charcoal transition-colors"
          >
            Caratteristiche
          </a>
          <a
            href="#how-it-works"
            className="text-sm font-medium text-muted-foreground hover:text-charcoal transition-colors"
          >
            Come funziona
          </a>
          {/* <a
            href="#testimonials"
            className="text-sm font-medium text-muted-foreground hover:text-charcoal transition-colors"
          >
            Testimonianze
          </a> */}
          <a
            href="#faq"
            className="text-sm font-medium text-muted-foreground hover:text-charcoal transition-colors"
          >
            FAQ
          </a>
        </nav>
        <Button
          className="bg-accent hover:bg-accent/90 text-white"
          size="sm"
          data-analytics="header-cta"
          onClick={() => {
            window.location.href = "https://hub.catchup4you.com";
          }}
        >
          Per le aziende
        </Button>
      </div>
    </header>
  );
};
export default Header;
