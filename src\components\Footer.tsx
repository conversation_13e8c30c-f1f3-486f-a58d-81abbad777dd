import React from "react";

import { FaFacebookF, FaInstagram, FaTiktok, FaYoutube } from "react-icons/fa";
import ButtonJoinWaitingList from "./ButtonJoinWaitingList";


const Footer: React.FC = () => {



  return (
    <footer className="bg-charcoal text-white">
      {/* --- HERO CTA ------------------------------------------------ */}
      <div className="section-container py-16 text-center">
        <h2 className="text-3xl md:text-4xl font-bold font-poppins mb-5">
          Il tuo assistente personale che si prende cura di te.

        </h2>

        
        <p className="text-lg text-gray-300 mb-8 max-w-xl mx-auto">
          Unisciti alla lista d'attesa e prova CatchUp in anteprima.
        </p>
        <ButtonJoinWaitingList   analyticsLabel="footer-c<PERSON>" />
      </div>

      {/* --- <PERSON><PERSON> FOOTER ------------------------------------------- */}
      <div className="border-t border-gray-800">
        <div className="section-container py-10 flex flex-col md:flex-row items-center justify-between space-y-8 md:space-y-0">
          {/* logo */}
          <img src="/icon_logo_512.png" alt="CatchUp" className="h-9" />

          {/* link legali */}
          <nav className="flex space-x-6 text-sm">
            <a href="/termini"  className="text-gray-400 hover:text-white">Termini</a>
            <a href="/privacy" className="text-gray-400 hover:text-white">Privacy</a>
            <a href="/cookie"  className="text-gray-400 hover:text-white">Cookie</a>
          </nav>

          {/* social */}
          <div className="flex space-x-6">
            <a href="https://www.facebook.com/people/CatchUp/61578186657743/" target="_blank" rel="noopener noreferrer" aria-label="Facebook" className="text-gray-400 hover:text-white"><FaFacebookF className="w-5 h-5" /></a>
            <a href="https://instagram.com/catchup4you" target="_blank" rel="noopener noreferrer" aria-label="Instagram" className="text-gray-400 hover:text-white"><FaInstagram className="w-5 h-5" /></a>
            <a href="https://tiktok.com/@catchup4you" target="_blank" rel="noopener noreferrer" aria-label="TikTok" className="text-gray-400 hover:text-white"><FaTiktok className="w-5 h-5" /></a>
            <a href="https://youtube.com/@catchup4you" target="_blank" rel="noopener noreferrer" aria-label="YouTube" className="text-gray-400 hover:text-white"><FaYoutube className="w-5 h-5" /></a>
          </div>

          {/* badge trust */}
          <div className="flex space-x-4">
            <span className="px-2 py-0.5 rounded bg-gray-700 text-xs">GDPR</span>
            <span className="px-2 py-0.5 rounded bg-gray-700 text-xs">SSL</span>
          </div>
        </div>

        <p className="text-center text-xs text-gray-500 pb-6">
          © 2025 CatchUp. Tutti i diritti riservati.
        </p>
      </div>
    </footer>
  );
};

export default Footer;