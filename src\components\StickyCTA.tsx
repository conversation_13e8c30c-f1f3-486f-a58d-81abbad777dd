
import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { useNavigate } from 'react-router-dom';

const StickyCTA: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const navigate = useNavigate();
  
  useEffect(() => {
    const handleScroll = () => {
      // Show the sticky CTA when user has scrolled past the hero section
      if (window.scrollY > 600) {
        setVisible(true);
      } else {
        setVisible(false);
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const handleJoinWaitlist = () => {
    navigate('/waitlist');
  };
  
  return (
    <div className={`sticky-cta transform transition-transform duration-300 ease-in-out ${visible ? 'translate-y-0' : 'translate-y-full'}`}>
      <div className="container flex justify-between items-center">
        <img 
          src="/lovable-uploads/e75a9161-9ec4-40c8-a9f7-fcbe16931728.png" 
          alt="CatchUp Logo" 
          className="h-6"
        />
        <Button 
          className="bg-accent hover:bg-accent/90 text-white transform hover:scale-105 transition-all duration-200" 
          size="sm"
          onClick={handleJoinWaitlist}
          data-analytics="sticky-cta"
        >
          Unisciti Ora!
        </Button>
      </div>
    </div>
  );
};

export default StickyCTA;
