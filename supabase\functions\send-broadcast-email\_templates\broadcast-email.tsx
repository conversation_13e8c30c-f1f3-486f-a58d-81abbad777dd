import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Text,
  Hr,
  Section,
} from 'npm:@react-email/components@0.0.22';
import * as React from 'npm:react@18.3.1';

interface BroadcastEmailProps {
  firstName: string;
  lastName: string;
  company?: string;
  content: string;
}

export const BroadcastEmail = ({
  firstName,
  lastName,
  company,
  content,
}: BroadcastEmailProps) => (
  <Html>
    <Head />
    <Preview>Aggiornamenti importanti da CatchUp</Preview>
    <Body style={main}>
      <Container style={container}>
        <Section style={logoContainer}>
          <Img
            src="https://ec2792b2-a97d-4eeb-aa29-cbf1c9b954a6.lovableproject.com/logo_full.png"
            width="200"
            height="auto"
            alt="CatchUp Logo"
            style={logo}
          />
        </Section>
        
        <Heading style={h1}>Ciao {firstName}! 👋</Heading>
        
        {company && (
          <Text style={greetingText}>
            Speriamo che tu e il team di <strong>{company}</strong> stiate bene!
          </Text>
        )}

        <div dangerouslySetInnerHTML={{ __html: content }} style={contentStyle} />

        <Hr style={hr} />

        <Text style={text}>
          Grazie per essere parte della community CatchUp! La tua presenza nella nostra lista d'attesa significa molto per noi.
        </Text>

        <Section style={socialContainer}>
          <Link href="https://linkedin.com/company/catchup" style={socialLink}>
            LinkedIn
          </Link>
          <Link href="https://twitter.com/catchup" style={socialLink}>
            Twitter
          </Link>
          <Link href="https://instagram.com/catchup" style={socialLink}>
            Instagram
          </Link>
        </Section>

        <Text style={footerText}>
          Hai domande? Rispondi direttamente a questa email - leggiamo ogni messaggio!
        </Text>

        <Text style={footerText}>
          A presto,<br/>
          <strong>Il Team CatchUp</strong>
        </Text>

        <Hr style={hr} />

        <Text style={disclaimer}>
          Hai ricevuto questa email perché ti sei iscritto alla lista d'attesa di CatchUp. 
          Se non dovessi più ricevere aggiornamenti, puoi disiscriverti in qualsiasi momento.
        </Text>
      </Container>
    </Body>
  </Html>
);

export default BroadcastEmail;

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Oxygen", Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  borderRadius: '8px',
  margin: '40px auto',
  padding: '40px',
  maxWidth: '600px',
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
};

const logoContainer = {
  textAlign: 'center' as const,
  marginBottom: '32px',
};

const logo = {
  margin: '0 auto',
};

const h1 = {
  color: '#1a1a1a',
  fontSize: '28px',
  fontWeight: '700',
  lineHeight: '1.3',
  margin: '0 0 24px',
};

const greetingText = {
  color: '#525252',
  fontSize: '16px',
  lineHeight: '1.6',
  margin: '0 0 24px',
};

const text = {
  color: '#525252',
  fontSize: '16px',
  lineHeight: '1.6',
  margin: '0 0 16px',
};

const contentStyle = {
  color: '#525252',
  fontSize: '16px',
  lineHeight: '1.6',
  margin: '24px 0',
};

const hr = {
  borderColor: '#e6e6e6',
  margin: '24px 0',
};

const socialContainer = {
  textAlign: 'center' as const,
  margin: '24px 0',
};

const socialLink = {
  color: '#6366f1',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  margin: '0 16px',
};

const footerText = {
  color: '#525252',
  fontSize: '16px',
  lineHeight: '1.6',
  margin: '24px 0 8px',
};

const disclaimer = {
  color: '#9ca3af',
  fontSize: '12px',
  lineHeight: '1.5',
  margin: '16px 0 0',
  textAlign: 'center' as const,
};