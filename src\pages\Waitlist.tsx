import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Progress } from "@/components/ui/progress";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { 
  CheckCircle, 
  Mail, 
  User, 
  Building, 
  MessageCircle, 
  Phone, 
  MessageSquare, 
  Send, 
  Linkedin,
  ArrowRight,
  ArrowLeft,
  Users,
  Sparkles,
  Shield,
  Zap,
  Clock
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

const waitlistSchema = z.object({
  firstName: z.string().min(2, "Il nome deve contenere almeno 2 caratteri"),
  lastName: z.string().min(2, "Il cognome deve contenere almeno 2 caratteri"),
  email: z.string().email("Inserisci un indirizzo email valido"),
  company: z.string().optional(),
  role: z.string().optional(),
  interests: z.string().optional(),
  marketingConsent: z.boolean().default(false),
  phone: z.string()
    .optional()
    .refine((val) => !val || /^[+]?[1-9][\d]{0,15}$/.test(val.replace(/[\s\-()]/g, '')), {
      message: "Inserisci un numero di telefono valido (es. +39 ************)"
    }),
  whatsapp: z.string()
    .optional()
    .refine((val) => !val || /^[+]?[1-9][\d]{0,15}$/.test(val.replace(/[\s\-()]/g, '')), {
      message: "Inserisci un numero WhatsApp valido (es. +39 ************)"
    }),
  telegram: z.string()
    .optional()
    .refine((val) => !val || /^@?[a-zA-Z0-9_]{5,32}$/.test(val), {
      message: "Inserisci un username Telegram valido (es. @username)"
    }),
  linkedin: z.string()
    .optional()
    .refine((val) => !val || /^(https?:\/\/)?(www\.)?linkedin\.com\/in\/[a-zA-Z0-9-]+\/?$/.test(val) || /^[a-zA-Z0-9-]+$/.test(val), {
      message: "Inserisci un profilo LinkedIn valido (URL o username)"
    }),
});

type WaitlistFormData = z.infer<typeof waitlistSchema>;

const STEPS = [
  { id: 1, title: "Informazioni Base", description: "Nome e email" },
  { id: 2, title: "Profilo Professionale", description: "Azienda e ruolo" },
  { id: 3, title: "Interessi", description: "Cosa ti entusiasma?" },
  { id: 4, title: "Contatti", description: "Come preferisci essere contattato?" },
];

const Waitlist = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [waitlistCount, setWaitlistCount] = useState(1247); // Mock count, can be fetched from API
  const { toast } = useToast();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    trigger,
    formState: { errors },
  } = useForm<WaitlistFormData>({
    resolver: zodResolver(waitlistSchema),
    mode: "onChange",
  });

  // Auto-increment waitlist counter for social proof
  useEffect(() => {
    const interval = setInterval(() => {
      setWaitlistCount(prev => prev + Math.floor(Math.random() * 3));
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  const nextStep = async () => {
    let fieldsToValidate: (keyof WaitlistFormData)[] = [];
    
    switch (currentStep) {
      case 1:
        fieldsToValidate = ["firstName", "lastName", "email"];
        break;
      case 2:
        // Optional fields, no validation needed
        break;
      case 3:
        // Optional fields, no validation needed
        break;
      case 4:
        fieldsToValidate = ["phone", "whatsapp", "telegram", "linkedin"];
        break;
    }

    if (fieldsToValidate.length > 0) {
      const isValid = await trigger(fieldsToValidate);
      if (!isValid) return;
    }

    setCurrentStep(prev => Math.min(prev + 1, STEPS.length));
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const onSubmit = async (data: WaitlistFormData) => {
    setIsSubmitting(true);
    
    const campaignId = "e5b7b032-80ee-4c8c-8e51-7626a2f7f6d5";

    try {
      // Insert into campaign_waitlist table
      const { data: waitlistData, error: waitlistError } = await supabase
        .from('campaign_waitlist')
        .insert([
          {
            campaign_id: campaignId,
            first_name: data.firstName,
            last_name: data.lastName,
            email: data.email,
            company: data.company,
            role: data.role,
            interests: data.interests,
            marketing_consent: data.marketingConsent,
            phone: data.phone,
            whatsapp: data.whatsapp,
            telegram: data.telegram,
            linkedin: data.linkedin,
          }
        ])
        .select()
        .single();

      if (waitlistError) {
        throw waitlistError;
      }

      // NOT . DO NOT REMOVE THIS COMMENT NEITHER ADD THIS CODE AS RLS POLICY DOES NOT ALLOWED ANON USER TO ADD USER TO CAMPAIGN
      // Add user to user_campaigns table
      // const { error: userCampaignError } = await supabase
      //   .from('user_campaigns')
      //   .insert({
      //     user_id: waitlistData.id,
      //     campaign_id: campaignId,
      //     status: 'ToSend'
      //   });

      // if (userCampaignError) {
      //   console.error("Error adding user to campaign:", userCampaignError);
      // }

      setIsSubmitted(true);
      setWaitlistCount(prev => prev + 1);
      toast({
        title: "🎉 Benvenuto nella famiglia!",
        description: "Sei ufficialmente nella lista. Preparati per qualcosa di straordinario!",
      });
    } catch (error) {
      console.error("Error submitting waitlist form:", error);
      toast({
        title: "Oops! Qualcosa è andato storto",
        description: "Non preoccuparti, riprova tra un attimo. Siamo qui per aiutarti!",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const progress = (currentStep / STEPS.length) * 100;

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-cyan-50 flex items-center justify-center p-4 relative overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-4 -left-4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
          <div className="absolute -top-4 -right-4 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
          <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
        </div>

        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="relative z-10"
        >
          <Card className="w-full max-w-md bg-white/80 backdrop-blur-lg border border-white/20 shadow-2xl">
            <CardContent className="pt-8 pb-8 px-8">
              <div className="text-center">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
                  className="mx-auto h-16 w-16 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center mb-6"
                >
                  <CheckCircle className="h-8 w-8 text-white" />
                </motion.div>
                
                <motion.h2 
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.4 }}
                  className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent mb-3"
                >
                  🎉 Benvenuto nella famiglia!
                </motion.h2>
                
                <motion.p 
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  className="text-gray-600 mb-6 leading-relaxed"
                >
                  Sei ufficialmente il <span className="font-semibold text-purple-600">#{waitlistCount}</span> della nostra community. 
                  Ti avviseremo non appena saremo pronti a rivoluzionare il tuo workflow!
                </motion.p>

                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.6 }}
                  className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 mb-6"
                >
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
                    <Clock className="h-4 w-4" />
                    <span>Tempo stimato di attesa: 2-3 settimane</span>
                  </div>
                </motion.div>
                
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.7 }}
                >
                  <Button 
                    onClick={() => window.location.href = '/'}
                    className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold py-3 rounded-lg transition-all duration-200 transform hover:scale-105"
                  >
                    Torna alla Home
                  </Button>
                </motion.div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    );
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <motion.div
            key="step1"
            initial={{ x: 20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: -20, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Iniziamo! 👋</h3>
              <p className="text-gray-600">Come dovremmo chiamarti?</p>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="firstName" className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <User className="h-4 w-4" />
                  Nome *
                </Label>
                <Input
                  id="firstName"
                  {...register("firstName")}
                  className="mt-2 h-12 rounded-lg border-gray-200 focus:border-purple-500 focus:ring-purple-500 transition-colors"
                  placeholder="Il tuo nome"
                />
                {errors.firstName && (
                  <motion.p 
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-red-500 text-sm mt-1"
                  >
                    {errors.firstName.message}
                  </motion.p>
                )}
              </div>

              <div>
                <Label htmlFor="lastName" className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <User className="h-4 w-4" />
                  Cognome *
                </Label>
                <Input
                  id="lastName"
                  {...register("lastName")}
                  className="mt-2 h-12 rounded-lg border-gray-200 focus:border-purple-500 focus:ring-purple-500 transition-colors"
                  placeholder="Il tuo cognome"
                />
                {errors.lastName && (
                  <motion.p 
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-red-500 text-sm mt-1"
                  >
                    {errors.lastName.message}
                  </motion.p>
                )}
              </div>

              <div>
                <Label htmlFor="email" className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <Mail className="h-4 w-4" />
                  Email *
                </Label>
                <Input
                  id="email"
                  type="email"
                  {...register("email")}
                  className="mt-2 h-12 rounded-lg border-gray-200 focus:border-purple-500 focus:ring-purple-500 transition-colors"
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <motion.p 
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-red-500 text-sm mt-1"
                  >
                    {errors.email.message}
                  </motion.p>
                )}
              </div>
            </div>
          </motion.div>
        );

      case 2:
        return (
          <motion.div
            key="step2"
            initial={{ x: 20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: -20, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Perfetto! 🏢</h3>
              <p className="text-gray-600">Parlaci del tuo background professionale</p>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="company" className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <Building className="h-4 w-4" />
                  Azienda <span className="text-gray-400">(opzionale)</span>
                </Label>
                <Input
                  id="company"
                  {...register("company")}
                  className="mt-2 h-12 rounded-lg border-gray-200 focus:border-purple-500 focus:ring-purple-500 transition-colors"
                  placeholder="Nome della tua azienda"
                />
              </div>

              <div>
                <Label htmlFor="role" className="text-sm font-medium text-gray-700">
                  Ruolo <span className="text-gray-400">(opzionale)</span>
                </Label>
                <Select onValueChange={(value) => setValue("role", value)}>
                  <SelectTrigger className="mt-2 h-12 rounded-lg border-gray-200 focus:border-purple-500 focus:ring-purple-500">
                    <SelectValue placeholder="Cosa fai principalmente?" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="founder">🚀 Fondatore/CEO</SelectItem>
                    <SelectItem value="developer">💻 Sviluppatore</SelectItem>
                    <SelectItem value="designer">🎨 Designer</SelectItem>
                    <SelectItem value="product-manager">📊 Product Manager</SelectItem>
                    <SelectItem value="marketer">📈 Marketer</SelectItem>
                    <SelectItem value="sales">💼 Vendite</SelectItem>
                    <SelectItem value="student">📚 Studente</SelectItem>
                    <SelectItem value="other">✨ Altro</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </motion.div>
        );

      case 3:
        return (
          <motion.div
            key="step3"
            initial={{ x: 20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: -20, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Fantastico! ✨</h3>
              <p className="text-gray-600">Cosa ti entusiasma di più della nostra piattaforma?</p>
            </div>

            <div>
              <Label htmlFor="interests" className="flex items-center gap-2 text-sm font-medium text-gray-700">
                <MessageCircle className="h-4 w-4" />
                I tuoi interessi <span className="text-gray-400">(opzionale)</span>
              </Label>
              <Textarea
                id="interests"
                {...register("interests")}
                className="mt-2 rounded-lg border-gray-200 focus:border-purple-500 focus:ring-purple-500 transition-colors resize-none"
                placeholder="Raccontaci cosa ti entusiasma... automazione, AI, produttività?"
                rows={4}
              />
            </div>

            <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <Sparkles className="h-5 w-5 text-purple-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Suggerimento</p>
                  <p className="text-sm text-gray-600">
                    Più ci racconti, meglio potremo personalizzare la tua esperienza!
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        );

      case 4:
        return (
          <motion.div
            key="step4"
            initial={{ x: 20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: -20, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Quasi fatto! 🎯</h3>
              <p className="text-gray-600">Come preferisci essere contattato?</p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="phone" className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <Phone className="h-4 w-4" />
                  Telefono
                </Label>
                <Input
                  id="phone"
                  {...register("phone")}
                  className="mt-2 h-12 rounded-lg border-gray-200 focus:border-purple-500 focus:ring-purple-500 transition-colors"
                  placeholder="+39 ************"
                />
                {errors.phone && (
                  <motion.p 
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-red-500 text-sm mt-1"
                  >
                    {errors.phone.message}
                  </motion.p>
                )}
              </div>

              <div>
                <Label htmlFor="whatsapp" className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <MessageSquare className="h-4 w-4" />
                  WhatsApp
                </Label>
                <Input
                  id="whatsapp"
                  {...register("whatsapp")}
                  className="mt-2 h-12 rounded-lg border-gray-200 focus:border-purple-500 focus:ring-purple-500 transition-colors"
                  placeholder="+39 ************"
                />
                {errors.whatsapp && (
                  <motion.p 
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-red-500 text-sm mt-1"
                  >
                    {errors.whatsapp.message}
                  </motion.p>
                )}
              </div>

              <div>
                <Label htmlFor="telegram" className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <Send className="h-4 w-4" />
                  Telegram
                </Label>
                <Input
                  id="telegram"
                  {...register("telegram")}
                  className="mt-2 h-12 rounded-lg border-gray-200 focus:border-purple-500 focus:ring-purple-500 transition-colors"
                  placeholder="@username"
                />
                {errors.telegram && (
                  <motion.p 
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-red-500 text-sm mt-1"
                  >
                    {errors.telegram.message}
                  </motion.p>
                )}
              </div>

              <div>
                <Label htmlFor="linkedin" className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <Linkedin className="h-4 w-4" />
                  LinkedIn
                </Label>
                <Input
                  id="linkedin"
                  {...register("linkedin")}
                  className="mt-2 h-12 rounded-lg border-gray-200 focus:border-purple-500 focus:ring-purple-500 transition-colors"
                  placeholder="linkedin.com/in/username"
                />
                {errors.linkedin && (
                  <motion.p 
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-red-500 text-sm mt-1"
                  >
                    {errors.linkedin.message}
                  </motion.p>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Checkbox
                  id="marketingConsent"
                  checked={watch("marketingConsent")}
                  onCheckedChange={(checked) => setValue("marketingConsent", checked as boolean)}
                  className="mt-1"
                />
                <Label htmlFor="marketingConsent" className="text-sm text-gray-600 leading-relaxed">
                  Sì, voglio ricevere aggiornamenti esclusivi, tips e opportunità di early access. 
                  <span className="block text-xs text-gray-500 mt-1">Puoi annullare l'iscrizione in qualsiasi momento.</span>
                </Label>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <Shield className="h-5 w-5 text-green-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">I tuoi dati sono al sicuro</p>
                    <p className="text-sm text-gray-600">
                      Non condividiamo mai le tue informazioni. Privacy garantita al 100%.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-cyan-50 py-8 px-4 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-4 -left-4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute -top-4 -right-4 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      <div className="relative z-10 max-w-2xl mx-auto">
        {/* Header with social proof */}
        <motion.div 
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm rounded-full px-4 py-2 mb-6 border border-white/20">
            <Users className="h-4 w-4 text-purple-600" />
            <span className="text-sm font-medium text-gray-700">
              {waitlistCount.toLocaleString()} persone si sono già unite
            </span>
            <Zap className="h-4 w-4 text-yellow-500 animate-pulse" />
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-purple-600 via-blue-600 to-cyan-600 bg-clip-text text-transparent mb-4">
            Unisciti alla Rivoluzione
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Sii tra i primi a sperimentare il futuro della produttività. 
            <span className="font-semibold text-purple-600"> Accesso anticipato garantito.</span>
          </p>
        </motion.div>

        {/* Progress indicator */}
        <motion.div 
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-8"
        >
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-lg">
            <div className="flex justify-between items-center mb-4">
              <span className="text-sm font-medium text-gray-600">
                Passaggio {currentStep} di {STEPS.length}
              </span>
              <span className="text-sm font-medium text-purple-600">
                {Math.round(progress)}% completato
              </span>
            </div>
            <Progress value={progress} className="h-2 mb-4" />
            <div className="grid grid-cols-4 gap-2">
              {STEPS.map((step, index) => (
                <div 
                  key={step.id}
                  className={`text-center p-2 rounded-lg transition-all duration-200 ${
                    index + 1 === currentStep 
                      ? 'bg-purple-100 text-purple-700' 
                      : index + 1 < currentStep 
                        ? 'bg-green-100 text-green-700' 
                        : 'bg-gray-50 text-gray-500'
                  }`}
                >
                  <div className="text-xs font-medium">{step.title}</div>
                  <div className="text-xs opacity-70 hidden sm:block">{step.description}</div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Form card */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Card className="bg-white/80 backdrop-blur-lg border border-white/20 shadow-2xl rounded-2xl overflow-hidden">
            <CardContent className="p-8">
              <form onSubmit={handleSubmit(onSubmit)}>
                <AnimatePresence mode="wait">
                  {renderStepContent()}
                </AnimatePresence>

                {/* Navigation buttons */}
                <div className="flex justify-between gap-4 mt-8">
                  {currentStep > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={prevStep}
                      className="flex items-center gap-2 px-6 py-3 rounded-lg border-gray-200 hover:border-purple-300 hover:text-purple-600 transition-colors"
                    >
                      <ArrowLeft className="h-4 w-4" />
                      Indietro
                    </Button>
                  )}
                  
                  <div className="flex-1" />
                  
                  {currentStep < STEPS.length ? (
                    <Button
                      type="button"
                      onClick={nextStep}
                      className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-lg transition-all duration-200 transform hover:scale-105"
                    >
                      Continua
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  ) : (
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="flex items-center gap-2 px-8 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-lg transition-all duration-200 transform hover:scale-105 disabled:scale-100"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                          Joining...
                        </>
                      ) : (
                        <>
                          <Sparkles className="h-4 w-4" />
                          Unisciti Ora!
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </form>
            </CardContent>
          </Card>
        </motion.div>

        {/* Footer note */}
                 <motion.div
           initial={{ y: 20, opacity: 0 }}
           animate={{ y: 0, opacity: 1 }}
           transition={{ duration: 0.6, delay: 0.3 }}
           className="text-center mt-8 text-sm text-gray-500"
         >
           <p>Nessun spam. Solo aggiornamenti di valore. Promesso. 🤝</p>
         </motion.div>
       </div>
     </div>
   );
};

export default Waitlist; 