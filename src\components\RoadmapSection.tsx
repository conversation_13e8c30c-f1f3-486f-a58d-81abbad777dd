
import React from 'react';

const RoadmapSection: React.FC = () => {
  return (
    <section className="bg-accent/5 py-16">
      <div className="section-container">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-2xl md:text-3xl font-bold mb-4 text-charcoal font-poppins">Da prenotazioni a vero Assistente Personale</h2>
          <p className="text-lg mb-8 text-muted-foreground">
            CatchUp evolve per gestire sempre più aspetti della tua vita quotidiana
          </p>
          
          <div className="p-6 md:p-8 bg-white rounded-xl shadow-md border border-border">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="p-4 border border-dashed border-accent/40 rounded-lg bg-accent/5">
                <h3 className="font-semibold text-lg mb-2 text-charcoal">Gestione Email</h3>
                <p className="text-muted-foreground text-sm">
                  Il tuo assistente leggerà e risponderà alle email importanti, trasformando le to-do list in "done" list.
                </p>
              </div>
              
              <div className="p-4 border border-dashed border-accent/40 rounded-lg bg-accent/5">
                <h3 className="font-semibold text-lg mb-2 text-charcoal">Integrazione Calendario</h3>
                <p className="text-muted-foreground text-sm">
                  CatchUp organizzerà la tua settimana in modo ottimale, conoscendo le tue preferenze e minimizzando gli spostamenti.
                </p>
              </div>
              
              <div className="p-4 border border-dashed border-accent/40 rounded-lg bg-accent/5">
                <h3 className="font-semibold text-lg mb-2 text-charcoal">Comunicazione Vocale Naturale</h3>
                <p className="text-muted-foreground text-sm">
                  Interagisci con un assistente vocale indistinguibile da un umano, eliminando attese e menu vocali.
                </p>
              </div>
              
              <div className="p-4 border border-dashed border-accent/40 rounded-lg bg-accent/5">
                <h3 className="font-semibold text-lg mb-2 text-charcoal">Memoria Perfetta</h3>
                <p className="text-muted-foreground text-sm">
                  Un assistente che ricorda ogni tua preferenza, corrispondenza e progetto, agendo in autonomia con la tua approvazione.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default RoadmapSection;
