import React from "react";
import { But<PERSON> } from "@/components/ui/button";

import ChatSimulation from "./ChatSimulation";
import AnimatedBackground from "./AnimatedBackground";
import Button<PERSON>oinWaitingList from "./ButtonJoinWaitingList";

const HeroSection: React.FC = () => {

  return (
    <section className="relative bg-gradient-to-b from-white to-muted/30 overflow-hidden">
      {/* Animated background elements */}
      <AnimatedBackground />

      <div className="section-container">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <div className="animate-fade-in">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-charcoal mb-6 font-poppins leading-tight">
              <span className="inline-block relative">
                <span className="relative z-10 bg-gradient-to-r from-orange-400 via-orange-500 to-pink-500 text-transparent bg-clip-text">
                  CatchUp
                </span>
              </span>
              <br />
              Prenota in un attimo, risparmia ogni volta..
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-muted-foreground relative">
              Il tuo assistente personale AI cerca e negozia servizi locali,
              <span className="relative">
                {" "}
                <br />
                quando a te conviene.
                <span className="absolute -bottom-1 left-0 w-full h-1 bg-accent/30 rounded"></span>
              </span>
              <br />
              Basta chattare o parlare.
            </p>
            <div className="flex flex-col items-start">
              <ButtonJoinWaitingList analyticsLabel="hero-cta" />
                

              {/* Trust Badge */}
              <div className="flex items-center mt-3 bg-gradient-to-r from-accent/10 to-accent/5 border border-accent/20 rounded-full px-3 py-1 shadow-sm">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 text-accent mr-1"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-xs font-medium text-charcoal">
                  Accesso anticipato gratuito
                </span>
              </div>
            </div>
          </div>

          <div className="relative animate-fade-in delay-200 md:ml-auto">
            <ChatSimulation className="mx-auto" />
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
